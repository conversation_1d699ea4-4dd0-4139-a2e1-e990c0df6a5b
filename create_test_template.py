from PIL import Image, ImageDraw, ImageFont
import os

# Create a simple test template
width, height = 600, 400
image = Image.new('RGB', (width, height), color='white')
draw = ImageDraw.Draw(image)

# Draw a simple border
draw.rectangle([10, 10, width-10, height-10], outline='black', width=3)

# Add some text
try:
    font = ImageFont.truetype("arial.ttf", 24)
except:
    font = ImageFont.load_default()

draw.text((50, 50), "TEST ID TEMPLATE", fill='black', font=font)
draw.text((50, 100), "Name: [Name will be here]", fill='blue', font=font)
draw.text((50, 140), "Position: [Position will be here]", fill='blue', font=font)
draw.text((50, 180), "Company: [Company will be here]", fill='blue', font=font)

# Add QR code placeholder
draw.rectangle([400, 80, 550, 230], outline='gray', width=2)
draw.text((420, 150), "QR", fill='gray', font=font)

# Save the image
image.save('test_template.png')
print("Test template created: test_template.png")
