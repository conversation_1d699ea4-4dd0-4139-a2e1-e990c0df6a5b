#!/usr/bin/env python3
"""
Test script for first run integration and trademark display.
This script tests the first run detection and setup flow.
"""

import os
import sys
import tempfile
import shutil
import unittest
from unittest.mock import patch, MagicMock

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import Flask app for testing
from app import app, is_first_run, CONFIG_PATH, DATASET_DIR, TEMPLATE_DIR

class TestFirstRunIntegration(unittest.TestCase):
    """Test cases for first run integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = app.test_client()
        self.app.testing = True
        
        # Create temporary directories for testing
        self.temp_dir = tempfile.mkdtemp()
        self.temp_config = os.path.join(self.temp_dir, "test_config.json")
        self.temp_dataset_dir = os.path.join(self.temp_dir, "datasets")
        self.temp_template_dir = os.path.join(self.temp_dir, "templates")
        
        os.makedirs(self.temp_dataset_dir, exist_ok=True)
        os.makedirs(self.temp_template_dir, exist_ok=True)
        
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_is_first_run_no_config(self):
        """Test first run detection when no config exists"""
        # Mock the CONFIG_PATH to point to non-existent file
        with patch('app.CONFIG_PATH', '/non/existent/config.json'):
            self.assertTrue(is_first_run())
    
    def test_is_first_run_no_sample_dataset(self):
        """Test first run detection when sample dataset doesn't exist"""
        # Create config file but no sample dataset
        with open(self.temp_config, 'w') as f:
            f.write('{"active_dataset": "test.csv"}')
        
        with patch('app.CONFIG_PATH', self.temp_config), \
             patch('app.DATASET_DIR', self.temp_dataset_dir):
            self.assertTrue(is_first_run())
    
    def test_is_first_run_with_existing_setup(self):
        """Test first run detection with existing setup"""
        # Create config file and sample dataset
        with open(self.temp_config, 'w') as f:
            f.write('{"active_dataset": "sample_employees_dataset.csv"}')
        
        sample_dataset = os.path.join(self.temp_dataset_dir, "sample_employees_dataset.csv")
        with open(sample_dataset, 'w') as f:
            f.write('ID,Name,Position,Company\n1,Test,Tester,TestCorp\n')
        
        with patch('app.CONFIG_PATH', self.temp_config), \
             patch('app.DATASET_DIR', self.temp_dataset_dir):
            self.assertFalse(is_first_run())
    
    def test_first_run_route_access(self):
        """Test that first run route is accessible"""
        response = self.app.get('/first_run')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'First Run Setup', response.data)
        self.assertIn(b'jLagzn STUDIO', response.data)
    
    def test_first_run_route_with_existing_files(self):
        """Test first run route displays existing files"""
        # Create some test files
        test_dataset = os.path.join(self.temp_dataset_dir, "test_dataset.csv")
        test_template = os.path.join(self.temp_template_dir, "test_template.png")
        
        with open(test_dataset, 'w') as f:
            f.write('ID,Name,Position,Company\n1,Test,Tester,TestCorp\n')
        
        with open(test_template, 'w') as f:
            f.write('fake image data')
        
        with patch('app.DATASET_DIR', self.temp_dataset_dir), \
             patch('app.TEMPLATE_DIR', self.temp_template_dir):
            response = self.app.get('/first_run')
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'test_dataset.csv', response.data)
            self.assertIn(b'test_template.png', response.data)
    
    def test_index_redirects_to_first_run(self):
        """Test that index redirects to first run when needed"""
        with patch('app.is_first_run', return_value=True):
            response = self.app.get('/')
            self.assertEqual(response.status_code, 302)
            self.assertIn('/first_run', response.location)
    
    def test_index_loads_normally_after_setup(self):
        """Test that index loads normally after setup"""
        # Create necessary files for normal operation
        with open(self.temp_config, 'w') as f:
            f.write('{"active_dataset": "test.csv", "active_template": "test.png"}')
        
        test_dataset = os.path.join(self.temp_dataset_dir, "test.csv")
        with open(test_dataset, 'w') as f:
            f.write('ID,Name,Position,Company\n1,Test,Tester,TestCorp\n')
        
        with patch('app.CONFIG_PATH', self.temp_config), \
             patch('app.DATASET_DIR', self.temp_dataset_dir), \
             patch('app.is_first_run', return_value=False):
            response = self.app.get('/')
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'Smart QR ID Scanner', response.data)
            self.assertIn(b'jLagzn STUDIO', response.data)

class TestTrademarkIntegration(unittest.TestCase):
    """Test cases for trademark and branding integration"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = app.test_client()
        self.app.testing = True
    
    def test_trademark_in_index_page(self):
        """Test that trademark appears in index page"""
        with patch('app.is_first_run', return_value=False), \
             patch('app.os.path.exists', return_value=True), \
             patch('app.pd.read_csv') as mock_read_csv:
            
            # Mock DataFrame
            mock_df = MagicMock()
            mock_df.columns = ['ID', 'Name', 'Position', 'Company']
            mock_df.__contains__ = lambda self, key: key in ['ID', 'Name', 'Position', 'Company']
            mock_read_csv.return_value = mock_df
            
            response = self.app.get('/')
            self.assertEqual(response.status_code, 200)
            self.assertIn(b'blurbgicon.png', response.data)
            self.assertIn(b'jLagzn STUDIO', response.data)
    
    def test_trademark_in_first_run_page(self):
        """Test that trademark appears in first run page"""
        response = self.app.get('/first_run')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'blurbgicon.png', response.data)
        self.assertIn(b'jLagzn STUDIO', response.data)
    
    def test_favicon_and_meta_tags(self):
        """Test that favicon and meta tags are present"""
        response = self.app.get('/first_run')
        self.assertEqual(response.status_code, 200)
        self.assertIn(b'rel="icon"', response.data)
        self.assertIn(b'rel="apple-touch-icon"', response.data)
        self.assertIn(b'name="author"', response.data)
        self.assertIn(b'jLagzn STUDIO', response.data)

class TestFormHandling(unittest.TestCase):
    """Test cases for form handling in first run"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.app = app.test_client()
        self.app.testing = True
        
        # Create temporary directories
        self.temp_dir = tempfile.mkdtemp()
        self.temp_dataset_dir = os.path.join(self.temp_dir, "datasets")
        self.temp_template_dir = os.path.join(self.temp_dir, "templates")
        self.temp_config = os.path.join(self.temp_dir, "config.json")
        
        os.makedirs(self.temp_dataset_dir, exist_ok=True)
        os.makedirs(self.temp_template_dir, exist_ok=True)
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_activate_with_existing_files(self):
        """Test activation with existing file selection"""
        # Create existing files
        existing_dataset = os.path.join(self.temp_dataset_dir, "existing.csv")
        existing_template = os.path.join(self.temp_template_dir, "existing.png")
        
        with open(existing_dataset, 'w') as f:
            f.write('ID,Name,Position,Company\n1,Test,Tester,TestCorp\n')
        
        with open(existing_template, 'w') as f:
            f.write('fake image data')
        
        with patch('app.DATASET_DIR', self.temp_dataset_dir), \
             patch('app.TEMPLATE_DIR', self.temp_template_dir), \
             patch('app.CONFIG_PATH', self.temp_config):
            
            response = self.app.post('/activate', data={
                'existing_dataset': 'existing.csv',
                'existing_template': 'existing.png',
                'triggeredBy': 'first_run'
            })
            
            # Should redirect to index with success
            self.assertEqual(response.status_code, 302)
            self.assertIn('success=true', response.location)

def run_tests():
    """Run all first run integration tests"""
    print("Running First Run Integration Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestFirstRunIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestTrademarkIntegration))
    suite.addTests(loader.loadTestsFromTestCase(TestFormHandling))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
