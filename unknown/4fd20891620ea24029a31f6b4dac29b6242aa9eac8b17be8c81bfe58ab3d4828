{"cells": [{"cell_type": "code", "execution_count": null, "id": "530c072a", "metadata": {}, "outputs": [], "source": ["import smtplib\n", "from email.message import EmailMessage\n", "import os\n", "\n", "def send_qr_email(to_email, qr_filename):\n", "    from_email = \"<EMAIL>\"  # Replace with your sender Gmail\n", "    app_password = \"laed tdlj xtbh ydtw\"  # Replace with your 16-character app password\n", "\n", "    msg = EmailMessage()\n", "    msg['Subject'] = \"Your Employee QR Code\"\n", "    msg['From'] = from_email\n", "    msg['To'] = to_email\n", "    msg.set_content(\"Attached is your QR code for ID verification.\")\n", "\n", "    # Read QR code image\n", "    with open(qr_filename, 'rb') as f:\n", "        file_data = f.read()\n", "        file_name = os.path.basename(qr_filename)\n", "\n", "    msg.add_attachment(file_data, maintype='image', subtype='png', filename=file_name)\n", "\n", "    # Send email via Gmail SMTP\n", "    with smtplib.SMTP_SSL('smtp.gmail.com', 465) as smtp:\n", "        smtp.login(from_email, app_password)\n", "        smtp.send_message(msg)\n", "\n", "    print(f\"Sent QR to {to_email}\")\n", "\n", "    send_qr_email(\"<EMAIL>\", \"static/qr_codes/001.png\")"]}, {"cell_type": "code", "execution_count": 1, "id": "bc044dc8", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting reportlabNote: you may need to restart the kernel to use updated packages.\n", "\n", "  Using cached reportlab-4.4.2-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: pillow>=9.0.0 in c:\\users\\<USER>\\anaconda3\\envs\\rover_runner\\lib\\site-packages (from reportlab) (10.4.0)\n", "Requirement already satisfied: charset-normalizer in c:\\users\\<USER>\\anaconda3\\envs\\rover_runner\\lib\\site-packages (from reportlab) (2.0.4)\n", "Using cached reportlab-4.4.2-py3-none-any.whl (2.0 MB)\n", "Installing collected packages: reportlab\n", "Successfully installed reportlab-4.4.2\n"]}], "source": ["%pip install reportlab"]}], "metadata": {"kernelspec": {"display_name": "rover_runner", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 5}