<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>Print Preview</title>
    <style>
      @page {
          size: {{ w }}in {{ h }}in;
          margin: 0;
      }
      html, body {
          margin: 0;
          padding: 0;
          background: white;
          height: 100%;
          position: relative;
          font-family: Arial, sans-serif;
      }
      .paper {
          width: {{ w }}in;
          height: {{ h }}in;
          overflow: hidden;
          margin: 0 auto;
          box-shadow: 0 0 10px rgba(0,0,0,0.2);
      }
      .paper img {
          width: 100%;
          height: 100%;
          object-fit: fill;
          display: block;
      }
      .preview-header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          background: rgba(255,255,255,0.9);
          padding: 10px 20px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          box-shadow: 0 2px 5px rgba(0,0,0,0.1);
          z-index: 1000;
      }
      .preview-title {
          font-weight: bold;
      }
      .preview-actions {
          display: flex;
          gap: 10px;
      }
      .preview-btn {
          background: #4a6baf;
          color: white;
          border: none;
          border-radius: 4px;
          padding: 8px 15px;
          font-size: 14px;
          cursor: pointer;
          display: flex;
          align-items: center;
          gap: 5px;
          transition: all 0.2s;
      }
      .preview-btn:hover {
          background: #3a5a9f;
          transform: translateY(-1px);
      }
      .preview-btn i {
          font-size: 12px;
      }

      @media print {
          .preview-header {
              display: none !important;
          }
          .paper {
              box-shadow: none;
          }
      }
    </style>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
    />
  </head>
  <body>
    <div class="preview-header">
      <div class="preview-title">Print Preview</div>
      <div class="preview-actions">
        <button class="preview-btn" id="backBtn">
          <i class="fas fa-arrow-left"></i> Back to Scanner
        </button>
        <button class="preview-btn" id="printBtn">
          <i class="fas fa-print"></i> Print
        </button>
        <button class="preview-btn" id="closeBtn">
          <i class="fas fa-times"></i> Close
        </button>
      </div>
    </div>
    <div class="paper">
      <img
        src="{{ url_for('static', filename='preview_' + session_id + '.png') }}"
      />
    </div>

    <script>
      // Store the referrer URL before printing
      const referrer = document.referrer;

      document.getElementById("backBtn").addEventListener("click", function () {
        // Return to the exact previous page
        if (referrer && referrer.includes(window.location.hostname)) {
          window.location.href = referrer;
        } else {
          window.location.href = "/";
        }
      });

      document
        .getElementById("printBtn")
        .addEventListener("click", function () {
          window.print();
        });

      document
        .getElementById("closeBtn")
        .addEventListener("click", function () {
          // For tabs opened with window.open()
          if (window.opener) {
            window.close();
          }
          // For regular navigation
          else if (history.length > 1) {
            history.back();
          } else {
            window.location.href = "/";
          }
        });

      // Auto-print if coming from the scanner
      if (document.referrer.includes("/print_image_direct")) {
        window.print();
      }
    </script>
  </body>
</html>
