#!/usr/bin/env python3
"""
Demo script to showcase the new email functionality in the ID QR Printing system.
This script demonstrates how to use the email features without running the full Flask app.
"""

import os
import sys
from email_service import EmailService
import pandas as pd
import qrcode
from cryptography.fernet import <PERSON><PERSON><PERSON>

def create_demo_qr_code():
    """Create a demo QR code for testing"""
    # Generate a simple QR code
    qr_data = "Demo QR Code - Employee ID: 001"
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(qr_data)
    qr.make(fit=True)
    
    # Create QR code image
    qr_img = qr.make_image(fill_color="black", back_color="white")
    
    # Save to temporary file
    demo_qr_path = "demo_qr_code.png"
    qr_img.save(demo_qr_path)
    
    return demo_qr_path

def demo_email_configuration():
    """Demonstrate email configuration"""
    print("🔧 Email Configuration Demo")
    print("-" * 40)
    
    # Create email service instance
    email_service = EmailService()
    
    print(f"Default SMTP Server: {email_service.smtp_server}")
    print(f"Default SMTP Port: {email_service.smtp_port}")
    print(f"Default Use TLS: {email_service.use_tls}")
    print(f"Default Sender Name: {email_service.sender_name}")
    
    # Update configuration
    print("\n📝 Updating configuration...")
    email_service.update_config(
        smtp_server="smtp.example.com",
        smtp_port=465,
        use_tls=False,
        sender_email="<EMAIL>",
        sender_password="demo_password",
        sender_name="Demo System"
    )
    
    print(f"Updated SMTP Server: {email_service.smtp_server}")
    print(f"Updated SMTP Port: {email_service.smtp_port}")
    print(f"Updated Use TLS: {email_service.use_tls}")
    print(f"Updated Sender Name: {email_service.sender_name}")
    print("✅ Configuration updated successfully!")

def demo_html_template():
    """Demonstrate HTML email template generation"""
    print("\n📧 HTML Email Template Demo")
    print("-" * 40)
    
    email_service = EmailService()
    
    # Sample employee data
    employee_data = {
        'ID': '001',
        'Name': 'John Doe',
        'Position': 'Software Engineer',
        'Company': 'Tech Corporation'
    }
    
    # Generate HTML template
    html_content = email_service._create_html_template('John Doe', employee_data)
    
    # Save HTML to file for inspection
    with open('demo_email_template.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("📄 HTML email template generated and saved to 'demo_email_template.html'")
    print("🔍 You can open this file in a web browser to see how the email will look")
    
    # Show a snippet of the HTML
    print("\n📋 Template snippet:")
    lines = html_content.split('\n')
    for i, line in enumerate(lines[20:30]):  # Show lines 20-30
        print(f"  {line.strip()}")
    print("  ...")

def demo_csv_processing():
    """Demonstrate CSV processing with email columns"""
    print("\n📊 CSV Processing Demo")
    print("-" * 40)
    
    # Create sample CSV data
    sample_data = {
        'ID': [1, 2, 3],
        'Name': ['John Doe', 'Jane Smith', 'Bob Johnson'],
        'Position': ['Engineer', 'Manager', 'Designer'],
        'Company': ['TechCorp', 'TechCorp', 'DesignCo'],
        'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>']
    }
    
    df = pd.DataFrame(sample_data)
    
    print("📋 Sample employee data with email addresses:")
    print(df.to_string(index=False))
    
    # Save to CSV
    csv_filename = 'demo_employees_with_emails.csv'
    df.to_csv(csv_filename, index=False)
    print(f"\n💾 Sample data saved to '{csv_filename}'")
    
    # Demonstrate handling CSV without email column
    df_no_email = df.drop('Email', axis=1)
    print("\n📋 Data without email column:")
    print(df_no_email.to_string(index=False))
    
    # Add empty email column (as the app would do)
    df_no_email['Email'] = ''
    print("\n📋 After adding empty email column:")
    print(df_no_email.to_string(index=False))

def demo_email_sending_simulation():
    """Simulate email sending (without actually sending)"""
    print("\n📤 Email Sending Simulation")
    print("-" * 40)
    
    # Create demo QR code
    qr_path = create_demo_qr_code()
    print(f"📱 Demo QR code created: {qr_path}")
    
    # Sample employee data
    employee_data = {
        'ID': '001',
        'Name': 'John Doe',
        'Position': 'Software Engineer',
        'Company': 'Tech Corporation'
    }
    
    email_service = EmailService()
    
    print("\n📧 Email sending simulation:")
    print(f"  📧 To: <EMAIL>")
    print(f"  👤 Employee: {employee_data['Name']}")
    print(f"  🆔 ID: {employee_data['ID']}")
    print(f"  💼 Position: {employee_data['Position']}")
    print(f"  🏢 Company: {employee_data['Company']}")
    print(f"  📎 Attachment: {qr_path}")
    
    print("\n✉️ Email would contain:")
    print("  - Personalized HTML template")
    print("  - Employee information")
    print("  - QR code as attachment")
    print("  - Professional styling")
    
    # Clean up
    if os.path.exists(qr_path):
        os.remove(qr_path)
        print(f"\n🧹 Cleaned up demo file: {qr_path}")

def demo_error_handling():
    """Demonstrate error handling scenarios"""
    print("\n⚠️ Error Handling Demo")
    print("-" * 40)
    
    email_service = EmailService()
    
    # Test with invalid configuration
    print("🔧 Testing with invalid SMTP configuration...")
    email_service.update_config(
        smtp_server="invalid.server.com",
        smtp_port=999,
        use_tls=True,
        sender_email="<EMAIL>",
        sender_password="wrong_password",
        sender_name="Test"
    )
    
    success, message = email_service.test_connection()
    print(f"  Result: {'✅ Success' if success else '❌ Failed'}")
    print(f"  Message: {message}")
    
    # Test with missing QR file
    print("\n📱 Testing with missing QR file...")
    employee_data = {'ID': '999', 'Name': 'Test User', 'Position': 'Tester', 'Company': 'TestCorp'}
    success, message = email_service.send_qr_email(
        '<EMAIL>',
        'Test User',
        'nonexistent_qr.png',
        employee_data
    )
    print(f"  Result: {'✅ Success' if success else '❌ Failed'}")
    print(f"  Message: {message}")

def main():
    """Run all demos"""
    print("🎯 ID QR Printing System - Email Features Demo")
    print("=" * 60)
    
    try:
        demo_email_configuration()
        demo_html_template()
        demo_csv_processing()
        demo_email_sending_simulation()
        demo_error_handling()
        
        print("\n🎉 Demo completed successfully!")
        print("\n📝 Files created during demo:")
        demo_files = [
            'demo_email_template.html',
            'demo_employees_with_emails.csv'
        ]
        
        for file in demo_files:
            if os.path.exists(file):
                print(f"  - {file}")
        
        print("\n💡 Next steps:")
        print("  1. Configure your email settings in the .env file")
        print("  2. Run the main application: python app.py")
        print("  3. Use the settings panel to configure email")
        print("  4. Scan QR codes and send them via email!")
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {str(e)}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
