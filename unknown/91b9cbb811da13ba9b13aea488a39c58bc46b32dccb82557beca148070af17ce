#!/usr/bin/env python3
"""
Test script for enhanced features including email column detection,
header normalization, and data cleanup functionality.
"""

import os
import sys
import unittest
import tempfile
import pandas as pd
import shutil

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import normalize_csv_headers, has_email_column, cleanup_previous_data

class TestEnhancedFeatures(unittest.TestCase):
    """Test cases for enhanced features"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_normalize_csv_headers_basic(self):
        """Test basic header normalization"""
        # Create DataFrame with various header formats
        df = pd.DataFrame({
            'id': [1, 2, 3],
            'NAME': ['<PERSON>', '<PERSON>', '<PERSON>'],
            'position': ['Engineer', 'Manager', 'Designer'],
            'COMPANY': ['TechCorp', 'TechCorp', 'DesignCo'],
            'email': ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        })
        
        # Normalize headers
        normalized_df = normalize_csv_headers(df)
        
        # Check that headers are normalized
        expected_headers = ['ID', 'Name', 'Position', 'Company', 'Email']
        self.assertEqual(list(normalized_df.columns), expected_headers)
    
    def test_normalize_csv_headers_variations(self):
        """Test header normalization with various email formats"""
        test_cases = [
            ('email', 'Email'),
            ('EMAIL', 'Email'),
            ('Email', 'Email'),
            ('e-mail', 'Email'),
            ('E-mail', 'Email'),
            ('E-Mail', 'Email'),
            ('mail', 'Email'),
            ('MAIL', 'Email'),
            ('Mail', 'Email')
        ]
        
        for input_header, expected_header in test_cases:
            df = pd.DataFrame({
                'ID': [1],
                'Name': ['Test'],
                'Position': ['Tester'],
                'Company': ['TestCorp'],
                input_header: ['<EMAIL>']
            })
            
            normalized_df = normalize_csv_headers(df)
            self.assertIn(expected_header, normalized_df.columns)
            self.assertEqual(normalized_df[expected_header].iloc[0], '<EMAIL>')
    
    def test_normalize_csv_headers_preserve_unknown(self):
        """Test that unknown headers are preserved"""
        df = pd.DataFrame({
            'ID': [1],
            'Name': ['Test'],
            'Position': ['Tester'],
            'Company': ['TestCorp'],
            'CustomField': ['CustomValue'],
            'AnotherField': ['AnotherValue']
        })
        
        normalized_df = normalize_csv_headers(df)
        
        # Check that known headers are normalized
        self.assertIn('ID', normalized_df.columns)
        self.assertIn('Name', normalized_df.columns)
        self.assertIn('Position', normalized_df.columns)
        self.assertIn('Company', normalized_df.columns)
        
        # Check that unknown headers are preserved
        self.assertIn('CustomField', normalized_df.columns)
        self.assertIn('AnotherField', normalized_df.columns)
    
    def test_has_email_column_with_emails(self):
        """Test email column detection with valid emails"""
        df = pd.DataFrame({
            'ID': [1, 2, 3],
            'Name': ['John', 'Jane', 'Bob'],
            'Position': ['Engineer', 'Manager', 'Designer'],
            'Company': ['TechCorp', 'TechCorp', 'DesignCo'],
            'Email': ['<EMAIL>', '<EMAIL>', '<EMAIL>']
        })
        
        self.assertTrue(has_email_column(df))
    
    def test_has_email_column_without_emails(self):
        """Test email column detection without email column"""
        df = pd.DataFrame({
            'ID': [1, 2, 3],
            'Name': ['John', 'Jane', 'Bob'],
            'Position': ['Engineer', 'Manager', 'Designer'],
            'Company': ['TechCorp', 'TechCorp', 'DesignCo']
        })
        
        self.assertFalse(has_email_column(df))
    
    def test_has_email_column_empty_emails(self):
        """Test email column detection with empty email column"""
        df = pd.DataFrame({
            'ID': [1, 2, 3],
            'Name': ['John', 'Jane', 'Bob'],
            'Position': ['Engineer', 'Manager', 'Designer'],
            'Company': ['TechCorp', 'TechCorp', 'DesignCo'],
            'Email': ['', '', '']
        })
        
        self.assertFalse(has_email_column(df))
    
    def test_has_email_column_partial_emails(self):
        """Test email column detection with some empty emails"""
        df = pd.DataFrame({
            'ID': [1, 2, 3],
            'Name': ['John', 'Jane', 'Bob'],
            'Position': ['Engineer', 'Manager', 'Designer'],
            'Company': ['TechCorp', 'TechCorp', 'DesignCo'],
            'Email': ['<EMAIL>', '', '<EMAIL>']
        })
        
        self.assertTrue(has_email_column(df))
    
    def test_has_email_column_na_values(self):
        """Test email column detection with NaN values"""
        df = pd.DataFrame({
            'ID': [1, 2, 3],
            'Name': ['John', 'Jane', 'Bob'],
            'Position': ['Engineer', 'Manager', 'Designer'],
            'Company': ['TechCorp', 'TechCorp', 'DesignCo'],
            'Email': [pd.NA, pd.NA, pd.NA]
        })
        
        self.assertFalse(has_email_column(df))
    
    def test_cleanup_previous_data(self):
        """Test cleanup of previous data"""
        # Create temporary QR folder with files
        qr_folder = os.path.join(self.temp_dir, "qr_codes")
        os.makedirs(qr_folder, exist_ok=True)
        
        # Create some test files
        test_files = ['001.png', '002.png', '003.png']
        for filename in test_files:
            file_path = os.path.join(qr_folder, filename)
            with open(file_path, 'w') as f:
                f.write('test content')
        
        # Create static folder with preview files
        static_folder = os.path.join(self.temp_dir, "static")
        os.makedirs(static_folder, exist_ok=True)
        
        preview_files = ['preview_123.png', 'preview_456.png', 'other_file.txt']
        for filename in preview_files:
            file_path = os.path.join(static_folder, filename)
            with open(file_path, 'w') as f:
                f.write('test content')
        
        # Verify files exist
        for filename in test_files:
            self.assertTrue(os.path.exists(os.path.join(qr_folder, filename)))
        
        for filename in preview_files:
            self.assertTrue(os.path.exists(os.path.join(static_folder, filename)))
        
        # Mock the global folders for cleanup function
        import app
        original_qr_folder = app.QR_FOLDER
        original_base_dir = app.BASE_DIR
        
        try:
            app.QR_FOLDER = qr_folder
            app.BASE_DIR = self.temp_dir
            
            # Run cleanup
            cleanup_previous_data()
            
            # Verify QR files are removed
            for filename in test_files:
                self.assertFalse(os.path.exists(os.path.join(qr_folder, filename)))
            
            # Verify preview files are removed but other files remain
            self.assertFalse(os.path.exists(os.path.join(static_folder, 'preview_123.png')))
            self.assertFalse(os.path.exists(os.path.join(static_folder, 'preview_456.png')))
            self.assertTrue(os.path.exists(os.path.join(static_folder, 'other_file.txt')))
            
        finally:
            # Restore original values
            app.QR_FOLDER = original_qr_folder
            app.BASE_DIR = original_base_dir

class TestCSVProcessing(unittest.TestCase):
    """Test CSV processing with various formats"""
    
    def test_csv_with_mixed_case_headers(self):
        """Test CSV processing with mixed case headers"""
        csv_content = """id,NAME,position,COMPANY,email
1,John Doe,Engineer,TechCorp,<EMAIL>
2,Jane Smith,Manager,TechCorp,<EMAIL>"""
        
        # Create temporary CSV file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_content)
            csv_path = f.name
        
        try:
            # Read and normalize
            df = pd.read_csv(csv_path)
            df = normalize_csv_headers(df)
            
            # Check normalized headers
            expected_headers = ['ID', 'Name', 'Position', 'Company', 'Email']
            self.assertEqual(list(df.columns), expected_headers)
            
            # Check data integrity
            self.assertEqual(df.loc[0, 'Name'], 'John Doe')
            self.assertEqual(df.loc[0, 'Email'], '<EMAIL>')
            self.assertEqual(df.loc[1, 'Name'], 'Jane Smith')
            self.assertEqual(df.loc[1, 'Email'], '<EMAIL>')
            
        finally:
            os.unlink(csv_path)
    
    def test_csv_with_email_variations(self):
        """Test CSV with different email column names"""
        test_cases = [
            'email',
            'EMAIL', 
            'Email',
            'e-mail',
            'E-Mail',
            'mail'
        ]
        
        for email_header in test_cases:
            csv_content = f"""ID,Name,Position,Company,{email_header}
1,John Doe,Engineer,TechCorp,<EMAIL>"""
            
            with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
                f.write(csv_content)
                csv_path = f.name
            
            try:
                df = pd.read_csv(csv_path)
                df = normalize_csv_headers(df)
                
                # Should normalize to 'Email'
                self.assertIn('Email', df.columns)
                self.assertEqual(df.loc[0, 'Email'], '<EMAIL>')
                
            finally:
                os.unlink(csv_path)

def run_tests():
    """Run all enhanced feature tests"""
    print("Running Enhanced Features Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestEnhancedFeatures))
    suite.addTests(loader.loadTestsFromTestCase(TestCSVProcessing))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
