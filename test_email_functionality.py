#!/usr/bin/env python3
"""
Test script for email functionality in the ID QR Printing system.
This script tests various email-related features including SMTP connection,
email sending, and error handling.
"""

import os
import sys
import unittest
from unittest.mock import patch, MagicMock
import tempfile
import json

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from email_service import EmailService
import pandas as pd

class TestEmailFunctionality(unittest.TestCase):
    """Test cases for email functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.email_service = EmailService()
        
        # Create a temporary QR code file for testing
        self.temp_qr_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        self.temp_qr_file.write(b'fake_qr_image_data')
        self.temp_qr_file.close()
        
        # Sample employee data
        self.employee_data = {
            'ID': '001',
            'Name': '<PERSON>',
            'Position': 'Software Engineer',
            'Company': 'Tech Corp'
        }
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_qr_file.name):
            os.unlink(self.temp_qr_file.name)
    
    def test_email_service_initialization(self):
        """Test that EmailService initializes with default values"""
        service = EmailService()
        self.assertEqual(service.smtp_server, 'smtp.gmail.com')
        self.assertEqual(service.smtp_port, 587)
        self.assertTrue(service.use_tls)
        self.assertEqual(service.sender_name, 'ID QR System')
    
    def test_update_config(self):
        """Test updating email configuration"""
        new_config = {
            'smtp_server': 'smtp.example.com',
            'smtp_port': 465,
            'use_tls': False,
            'sender_email': '<EMAIL>',
            'sender_password': 'password123',
            'sender_name': 'Test System'
        }
        
        self.email_service.update_config(**new_config)
        
        self.assertEqual(self.email_service.smtp_server, 'smtp.example.com')
        self.assertEqual(self.email_service.smtp_port, 465)
        self.assertFalse(self.email_service.use_tls)
        self.assertEqual(self.email_service.sender_email, '<EMAIL>')
        self.assertEqual(self.email_service.sender_password, 'password123')
        self.assertEqual(self.email_service.sender_name, 'Test System')
    
    @patch('smtplib.SMTP')
    def test_connection_success(self, mock_smtp):
        """Test successful SMTP connection"""
        # Mock successful connection
        mock_server = MagicMock()
        mock_smtp.return_value = mock_server
        
        self.email_service.sender_email = '<EMAIL>'
        self.email_service.sender_password = 'password123'
        
        success, message = self.email_service.test_connection()
        
        self.assertTrue(success)
        self.assertEqual(message, "Connection successful")
        mock_server.starttls.assert_called_once()
        mock_server.login.assert_called_once_with('<EMAIL>', 'password123')
        mock_server.quit.assert_called_once()
    
    @patch('smtplib.SMTP')
    def test_connection_failure(self, mock_smtp):
        """Test SMTP connection failure"""
        # Mock connection failure
        mock_smtp.side_effect = Exception("Connection failed")
        
        self.email_service.sender_email = '<EMAIL>'
        self.email_service.sender_password = 'password123'
        
        success, message = self.email_service.test_connection()
        
        self.assertFalse(success)
        self.assertEqual(message, "Connection failed")
    
    @patch('smtplib.SMTP')
    def test_send_email_success(self, mock_smtp):
        """Test successful email sending"""
        # Mock successful email sending
        mock_server = MagicMock()
        mock_smtp.return_value = mock_server
        
        self.email_service.sender_email = '<EMAIL>'
        self.email_service.sender_password = 'password123'
        
        success, message = self.email_service.send_qr_email(
            '<EMAIL>',
            'John Doe',
            self.temp_qr_file.name,
            self.employee_data
        )
        
        self.assertTrue(success)
        self.assertEqual(message, "Email sent successfully")
        mock_server.starttls.assert_called_once()
        mock_server.login.assert_called_once_with('<EMAIL>', 'password123')
        mock_server.send_message.assert_called_once()
        mock_server.quit.assert_called_once()
    
    @patch('smtplib.SMTP')
    def test_send_email_failure(self, mock_smtp):
        """Test email sending failure"""
        # Mock email sending failure
        mock_smtp.side_effect = Exception("Send failed")
        
        self.email_service.sender_email = '<EMAIL>'
        self.email_service.sender_password = 'password123'
        
        success, message = self.email_service.send_qr_email(
            '<EMAIL>',
            'John Doe',
            self.temp_qr_file.name,
            self.employee_data
        )
        
        self.assertFalse(success)
        self.assertEqual(message, "Send failed")
    
    def test_send_email_missing_qr_file(self):
        """Test email sending with missing QR file"""
        self.email_service.sender_email = '<EMAIL>'
        self.email_service.sender_password = 'password123'
        
        # Use a non-existent file path
        success, message = self.email_service.send_qr_email(
            '<EMAIL>',
            'John Doe',
            '/non/existent/file.png',
            self.employee_data
        )
        
        # Should still attempt to send email even without QR file
        self.assertFalse(success)  # Will fail due to missing SMTP config in test
    
    def test_html_template_generation(self):
        """Test HTML email template generation"""
        html_content = self.email_service._create_html_template('John Doe', self.employee_data)
        
        # Check that the HTML contains expected content
        self.assertIn('John Doe', html_content)
        self.assertIn('001', html_content)
        self.assertIn('Software Engineer', html_content)
        self.assertIn('Tech Corp', html_content)
        self.assertIn('<!DOCTYPE html>', html_content)
        self.assertIn('Employee QR Code', html_content)

class TestEmailIntegration(unittest.TestCase):
    """Integration tests for email functionality with the main app"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Create a temporary CSV file for testing
        self.temp_csv = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        self.temp_csv.write('ID,Name,Position,Company,Email\n')
        self.temp_csv.write('1,John Doe,Engineer,TechCorp,<EMAIL>\n')
        self.temp_csv.write('2,Jane Smith,Manager,TechCorp,<EMAIL>\n')
        self.temp_csv.close()
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_csv.name):
            os.unlink(self.temp_csv.name)
    
    def test_csv_with_email_column(self):
        """Test CSV processing with email column"""
        df = pd.read_csv(self.temp_csv.name)
        
        # Check that email column exists and has expected values
        self.assertIn('Email', df.columns)
        self.assertEqual(df.loc[0, 'Email'], '<EMAIL>')
        self.assertEqual(df.loc[1, 'Email'], '<EMAIL>')
    
    def test_csv_without_email_column(self):
        """Test CSV processing without email column"""
        # Create CSV without email column
        temp_csv_no_email = tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False)
        temp_csv_no_email.write('ID,Name,Position,Company\n')
        temp_csv_no_email.write('1,John Doe,Engineer,TechCorp\n')
        temp_csv_no_email.close()
        
        try:
            df = pd.read_csv(temp_csv_no_email.name)
            
            # Add empty email column as the app would do
            if 'Email' not in df.columns:
                df['Email'] = ''
            
            self.assertIn('Email', df.columns)
            self.assertEqual(df.loc[0, 'Email'], '')
        finally:
            os.unlink(temp_csv_no_email.name)

def run_tests():
    """Run all email functionality tests"""
    print("Running Email Functionality Tests...")
    print("=" * 50)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestEmailFunctionality))
    suite.addTests(loader.loadTestsFromTestCase(TestEmailIntegration))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    return result.wasSuccessful()

if __name__ == '__main__':
    success = run_tests()
    sys.exit(0 if success else 1)
