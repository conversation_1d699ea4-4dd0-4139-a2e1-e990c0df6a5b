<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Print ID</title>
    <style id="dynamic-print-style"></style>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        background: white;
        font-family: Arial, sans-serif;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        overflow: hidden;
      }

      .print-container {
        width: 100vw;
        height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .id-card {
        position: relative;
        width: 3.375in;
        height: 2.125in;
        overflow: hidden;
        border: 1px solid #ccc;
        background-color: white;
      }

      #template-image {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        object-fit: cover;
        z-index: 0;
      }

      .overlay-text {
        position: absolute;
        top: 45%;
        left: 0;
        width: 100%;
        text-align: center;
        color: black;
        z-index: 1;
        font-family: Arial, sans-serif;
        line-height: 1.2;
        padding: 0 0.2in;
        box-sizing: border-box;
      }

      .overlay-text #preview-name {
        font-size: 1.2em;
        font-weight: bold;
      }

      .overlay-text #preview-position {
        font-size: 1em;
        margin-top: 0.2em;
      }

      .overlay-text #preview-company {
        font-size: 0.9em;
        margin-top: 0.2em;
      }

      @media print {
        html,
        body {
          margin: 0;
          padding: 0;
          overflow: hidden;
        }

        .print-container {
          width: 100vw;
          height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .id-card {
          margin: auto;
        }
      }
    </style>
  </head>
  <body>
    <div class="print-container">
      <div class="id-card" id="id-card">
        <img id="template-image" src="" alt="Template" />
        <div class="overlay-text">
          <div id="preview-name">Name</div>
          <div id="preview-position">Position</div>
          <div id="preview-company">Company</div>
        </div>
      </div>
    </div>

    <script>
      function getQueryParam(name) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(name);
      }

      function applyDimensions(widthIn, heightIn) {
        const idCard = document.getElementById("id-card");

        idCard.style.width = `${widthIn}in`;
        idCard.style.height = `${heightIn}in`;

        const styleTag = document.getElementById("dynamic-print-style");
        styleTag.innerHTML = `@page { size: ${widthIn}in ${heightIn}in; margin: 0; }`;
      }

      window.onload = () => {
        const name = getQueryParam("id_name") || "Name";
        const position = getQueryParam("id_position") || "Position";
        const company = getQueryParam("id_company") || "Company";
        const templateUrl = getQueryParam("template");
        const paperSize = getQueryParam("paper_size");
        const customWidth = parseFloat(getQueryParam("custom_width")) || null;
        const customHeight = parseFloat(getQueryParam("custom_height")) || null;

        const sizeMap = {
          A4: [8.27, 11.69],
          Letter: [8.5, 11],
          Legal: [8.5, 14],
          A3: [11.7, 16.5],
          A5: [5.8, 8.3],
          A6: [4.1, 5.8],
          A7: [2.9, 4.1],
          B5: [7.2, 10.1],
          B4: [9.8, 13.9],
          "4x6": [4, 6],
          "5x7": [5, 7],
          "5x8": [5, 8],
          "9x13": [3.5, 5],
        };

        let widthIn = 3.375,
          heightIn = 2.125;
        if (paperSize === "custom" && customWidth && customHeight) {
          widthIn = customWidth;
          heightIn = customHeight;
        } else if (sizeMap[paperSize]) {
          [widthIn, heightIn] = sizeMap[paperSize];
        }

        applyDimensions(widthIn, heightIn);

        document.getElementById("preview-name").textContent = name;
        document.getElementById("preview-position").textContent = position;
        document.getElementById("preview-company").textContent = company;

        const img = document.getElementById("template-image");
        img.src = templateUrl;

        img.onload = () => {
          window.print();
          setTimeout(() => window.close(), 500);
        };
      };
    </script>
  </body>
</html>
