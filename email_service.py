import smtplib
import os
from email.message import EmailMessage
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.image import MIMEImage
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self):
        self.smtp_server = os.getenv('SMTP_SERVER', 'smtp.gmail.com')
        self.smtp_port = int(os.getenv('SMTP_PORT', '587'))
        self.use_tls = os.getenv('SMTP_USE_TLS', 'True').lower() == 'true'
        self.sender_email = os.getenv('SENDER_EMAIL', '')
        self.sender_password = os.getenv('SENDER_PASSWORD', '')
        self.sender_name = os.getenv('SENDER_NAME', 'ID QR System')
        
    def test_connection(self):
        """Test SMTP connection and authentication"""
        try:
            if self.use_tls:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()
            else:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            
            server.login(self.sender_email, self.sender_password)
            server.quit()
            return True, "Connection successful"
        except Exception as e:
            logger.error(f"SMTP connection failed: {str(e)}")
            return False, str(e)
    
    def send_qr_email(self, recipient_email, recipient_name, qr_image_path, employee_data):
        """Send QR code via email with HTML template"""
        try:
            # Create message
            msg = MIMEMultipart('related')
            msg['Subject'] = f"Your Employee QR Code - {employee_data.get('Name', 'Employee')}"
            msg['From'] = f"{self.sender_name} <{self.sender_email}>"
            msg['To'] = recipient_email
            
            # Create HTML content
            html_content = self._create_html_template(recipient_name, employee_data)
            
            # Attach HTML content
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            # Attach QR code image
            if os.path.exists(qr_image_path):
                with open(qr_image_path, 'rb') as f:
                    img_data = f.read()
                    img = MIMEImage(img_data, _subtype='png')
                    img.add_header('Content-ID', '<qr_code>')
                    img.add_header('Content-Disposition', 'attachment', filename=f"qr_code_{employee_data.get('ID', 'unknown')}.png")
                    msg.attach(img)
            
            # Send email
            if self.use_tls:
                server = smtplib.SMTP(self.smtp_server, self.smtp_port)
                server.starttls()
            else:
                server = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
            
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()
            
            logger.info(f"QR code email sent successfully to {recipient_email}")
            return True, "Email sent successfully"
            
        except Exception as e:
            logger.error(f"Failed to send email to {recipient_email}: {str(e)}")
            return False, str(e)
    
    def _create_html_template(self, recipient_name, employee_data):
        """Create HTML email template"""
        return f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Your Employee QR Code</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                    background-color: #f9f9f9;
                }}
                .container {{
                    background: white;
                    border-radius: 10px;
                    padding: 30px;
                    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                }}
                .header {{
                    text-align: center;
                    border-bottom: 3px solid #007bff;
                    padding-bottom: 20px;
                    margin-bottom: 30px;
                }}
                .header h1 {{
                    color: #007bff;
                    margin: 0;
                    font-size: 28px;
                }}
                .employee-info {{
                    background: #f8f9fa;
                    border-radius: 8px;
                    padding: 20px;
                    margin: 20px 0;
                }}
                .info-row {{
                    display: flex;
                    justify-content: space-between;
                    margin: 10px 0;
                    padding: 8px 0;
                    border-bottom: 1px solid #dee2e6;
                }}
                .info-label {{
                    font-weight: bold;
                    color: #495057;
                }}
                .info-value {{
                    color: #212529;
                }}
                .qr-section {{
                    text-align: center;
                    margin: 30px 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    border-radius: 10px;
                    color: white;
                }}
                .footer {{
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #dee2e6;
                    color: #6c757d;
                    font-size: 14px;
                }}
                .btn {{
                    display: inline-block;
                    padding: 12px 24px;
                    background: #28a745;
                    color: white;
                    text-decoration: none;
                    border-radius: 5px;
                    margin: 10px;
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🆔 Employee QR Code</h1>
                    <p>Your personalized QR code for ID verification</p>
                </div>
                
                <p>Dear {recipient_name},</p>
                
                <p>Your employee QR code has been generated and is ready for use. Please find your QR code attached to this email.</p>
                
                <div class="employee-info">
                    <h3>📋 Employee Information</h3>
                    <div class="info-row">
                        <span class="info-label">Employee ID:</span>
                        <span class="info-value">{employee_data.get('ID', 'N/A')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Name:</span>
                        <span class="info-value">{employee_data.get('Name', 'N/A')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Position:</span>
                        <span class="info-value">{employee_data.get('Position', 'N/A')}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">Company:</span>
                        <span class="info-value">{employee_data.get('Company', 'N/A')}</span>
                    </div>
                </div>
                
                <div class="qr-section">
                    <h3>📱 Your Personal QR Code</h3>
                    <p><strong>QR Code Contents:</strong></p>
                    <div style="background: rgba(255,255,255,0.2); padding: 15px; border-radius: 8px; margin: 15px 0;">
                        <div style="font-family: monospace; font-size: 16px; line-height: 1.8;">
                            <div>🆔 <strong>ID:</strong> {employee_data.get('ID', 'N/A')}</div>
                            <div>👤 <strong>Name:</strong> {employee_data.get('Name', 'N/A')}</div>
                            <div>💼 <strong>Position:</strong> {employee_data.get('Position', 'N/A')}</div>
                        </div>
                    </div>
                    <p>Your QR code is attached as an image file. You can:</p>
                    <ul style="text-align: left; display: inline-block;">
                        <li>Save it to your phone</li>
                        <li>Print it for physical use</li>
                        <li>Use it for ID verification and printing</li>
                    </ul>
                </div>
                
                <div class="footer">
                    <p><strong>Important:</strong> Keep your QR code secure and do not share it with unauthorized persons.</p>
                    <p>This is an automated message from the ID QR System.</p>
                    <p>© 2025 ID QR System. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>
        """
    
    def update_config(self, smtp_server, smtp_port, use_tls, sender_email, sender_password, sender_name):
        """Update email configuration"""
        self.smtp_server = smtp_server
        self.smtp_port = smtp_port
        self.use_tls = use_tls
        self.sender_email = sender_email
        self.sender_password = sender_password
        self.sender_name = sender_name
        
        # Save to environment (in a real app, you'd save to a config file or database)
        os.environ['SMTP_SERVER'] = smtp_server
        os.environ['SMTP_PORT'] = str(smtp_port)
        os.environ['SMTP_USE_TLS'] = str(use_tls)
        os.environ['SENDER_EMAIL'] = sender_email
        os.environ['SENDER_PASSWORD'] = sender_password
        os.environ['SENDER_NAME'] = sender_name

# Global email service instance
email_service = EmailService()
